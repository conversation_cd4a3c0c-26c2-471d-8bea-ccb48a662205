import 'package:riverpod_annotation/riverpod_annotation.dart';

import 'home_state.dart';

part 'home_provider.g.dart';

@riverpod
class Home extends _$Home {
  @override
  HomeState build() => const HomeState();

  void setIsInBackground(bool isInBackground) {
    state = state.copyWith(isInBackground: isInBackground);
  }

  void setTab(int index) {
    state = state.copyWith(currentIndex: index);
  }

}
