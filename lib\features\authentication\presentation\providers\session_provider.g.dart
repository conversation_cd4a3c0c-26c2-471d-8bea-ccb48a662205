// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'session_provider.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$userSessionHash() => r'dd30aa9a058f251356c61969f37cbd76cac59246';

/// Session provider that manages user session state
/// This provider is auto-dispose and will be disposed when no longer watched
/// Other providers can depend on this to be automatically cleaned up on logout
///
/// Copied from [UserSession].
@ProviderFor(UserSession)
final userSessionProvider =
    AutoDisposeNotifierProvider<UserSession, bool>.internal(
  UserSession.new,
  name: r'userSessionProvider',
  debugGetCreateSourceHash:
      const bool.fromEnvironment('dart.vm.product') ? null : _$userSessionHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef _$UserSession = AutoDisposeNotifier<bool>;
// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member
