import 'package:flutter_audio_room/core/di/app_module.dart';
import 'package:flutter_audio_room/features/audio_room/data/model/other_user_info_model.dart';
import 'package:flutter_audio_room/features/authentication/presentation/providers/session_provider.dart';
import 'package:flutter_audio_room/features/authentication/presentation/providers/user_info_cache_service.dart';
import 'package:flutter_audio_room/features/my/data/model/follow_user_info_model.dart';
import 'package:flutter_audio_room/features/my/data/repositories/follow_repository_provider.dart';
import 'package:flutter_audio_room/features/my/domain/entities/follow_origin.dart';
import 'package:flutter_audio_room/features/my/domain/repositories/i_follow_repository.dart';
import 'package:flutter_audio_room/features/my/presentation/provider/follow_state.dart';
import 'package:flutter_audio_room/shared/domain/models/either.dart';
import 'package:flutter_audio_room/shared/domain/models/paginated_response.dart';
import 'package:flutter_audio_room/shared/domain/types/common_types.dart';
import 'package:flutter_audio_room/shared/exceptions/error_handler.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';

part 'follow_provider.g.dart';

@Riverpod(keepAlive: true)
class Follow extends _$Follow {
  late IFollowRepository _repository;
  late UserInfoCacheService _cacheService;

  @override
  FollowState build() {
    // Watch userSession to establish dependency relationship
    // When userSession is disposed, this provider will also be disposed
    ref.watch(userSessionProvider);

    _repository = ref.watch(followRepositoryProvider);
    _cacheService = getIt<UserInfoCacheService>();
    return const FollowState();
  }

  void setFollowCount(OtherUserInfoModel data) {
    state = state.copyWith(
      followerCount: data.followerCount,
      followingCount: data.followeeCount,
      mutualFollowCount: data.mutualFollowCount,
    );
  }

  /// Load follower list with pagination
  Future<ResultWithData<PaginatedResponse<FollowUserInfoModel>>> loadFollowers(
      {int current = 1, int size = 10}) async {
    try {
      final result = await _repository.getFollowerList(
        current: current,
        size: size,
      );

      return result.fold((e) {
        return Left(ErrorHandler.createValidationError(e.toString()));
      }, (r) {
        var res = r.copyWith();
        final list = res.records.map((e) {
          return e.copyWith(
            isFollower: true,
          );
        }).toList();

        res = res.copyWith(records: list);

        state = state.copyWith(
          followers: res,
        );
        return Right(res);
      });
    } catch (e) {
      return Left(ErrorHandler.createValidationError(e.toString()));
    }
  }

  /// Load following list with pagination
  Future<ResultWithData<PaginatedResponse<FollowUserInfoModel>>> loadFollowing(
      {int current = 1, int size = 10}) async {
    try {
      final result = await _repository.getFollowingList(
        current: current,
        size: size,
      );

      return result.fold((e) {
        return Left(ErrorHandler.createValidationError(e.toString()));
      }, (r) {
        var res = r.copyWith();
        final list = res.records.map((e) {
          return e.copyWith(
            isFollowing: true,
          );
        }).toList();

        res = res.copyWith(records: list);

        state = state.copyWith(
          following: res,
        );
        return Right(res);
      });
    } catch (e) {
      return Left(ErrorHandler.createValidationError(e.toString()));
    }
  }

  /// Follow a user
  Future<VoidResult> followUser(String userId,
      {required FollowOrigin origin}) async {
    try {
      final result = await _repository.followUser(
        userId: userId,
        origin: origin,
      );

      if (result.isRight()) {
        // 更新缓存中的用户信息
        _updateUserCacheAfterFollow(userId, isFollowing: true);
        return const Right(null);
      } else {
        return Left(
            ErrorHandler.createValidationError('Failed to follow user'));
      }
    } catch (e) {
      return Left(ErrorHandler.createValidationError(e.toString()));
    }
  }

  /// UnFollow a user
  Future<VoidResult> unFollowUser(String userId) async {
    try {
      final result = await _repository.unFollowUser(userId: userId);

      if (result.isRight()) {
        // 更新缓存中的用户信息
        _updateUserCacheAfterFollow(userId, isFollowing: false);
        return const Right(null);
      } else {
        return Left(
            ErrorHandler.createValidationError('Failed to unfollow user'));
      }
    } catch (e) {
      return Left(ErrorHandler.createValidationError(e.toString()));
    }
  }

  /// 更新缓存中的用户关注状态
  void _updateUserCacheAfterFollow(String userId, {required bool isFollowing}) {
    // 使用缓存服务的专用方法来更新关注状态
    _cacheService.updateUserFollowStatus(
      userId,
      isFollowing: isFollowing,
      // 当前用户关注/取消关注其他用户时，被关注用户的粉丝数会变化
      followerCountDelta: isFollowing ? 1 : -1,
    );
  }
}
