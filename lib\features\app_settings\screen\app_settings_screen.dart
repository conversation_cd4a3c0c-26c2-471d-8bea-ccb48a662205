import 'package:easy_debounce/easy_throttle.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_audio_room/core/di/app_module.dart';
import 'package:flutter_audio_room/core/extensions/context_ext.dart';
import 'package:flutter_audio_room/core/extensions/navigator_ext.dart';
import 'package:flutter_audio_room/core/utils/loading_utils.dart';
import 'package:flutter_audio_room/core/utils/localization_utils.dart';
import 'package:flutter_audio_room/core/utils/log_utils.dart';
import 'package:flutter_audio_room/core/widgets/app_scaffold.dart';
import 'package:flutter_audio_room/features/app_settings/screen/blacklist_screen.dart';
import 'package:flutter_audio_room/features/app_settings/screen/change_password_screen.dart';
import 'package:flutter_audio_room/features/app_settings/screen/delete_account_screen.dart';
import 'package:flutter_audio_room/features/audio_room/domain/interfaces/i_mini_player_service.dart';
import 'package:flutter_audio_room/features/audio_room/presentation/providers/audio_room_provider.dart';
import 'package:flutter_audio_room/features/authentication/presentation/providers/auth_providers.dart';
import 'package:flutter_audio_room/features/authentication/presentation/screens/authentication_main_screen.dart';
import 'package:flutter_audio_room/features/home/<USER>/providers/home_provider.dart';
import 'package:flutter_audio_room/features/instant_chat/presentation/provider/instant_call_provider.dart';
import 'package:flutter_audio_room/features/voice_call/friend/provider/friend_voice_call_provider.dart';
import 'package:flutter_audio_room/main.dart';
import 'package:flutter_audio_room/services/gift_service/presentation/provider/gift_provider.dart';
import 'package:flutter_audio_room/shared/data/remote/network_service.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

class AppSettingsOptions {
  final String title;
  final IconData? icon;
  final VoidCallback onPressed;
  final Color? iconColor;
  final Widget? trailing;
  final bool showDivider;

  const AppSettingsOptions({
    required this.title,
    this.icon,
    required this.onPressed,
    this.iconColor,
    this.trailing,
    this.showDivider = true,
  });
}

class SettingsSection {
  final String? title;
  final List<AppSettingsOptions> options;
  final EdgeInsetsGeometry? padding;

  const SettingsSection({
    this.title,
    required this.options,
    this.padding,
  });
}

class LogoutController {
  ProviderContainer ref;
  BuildContext context;

  LogoutController(this.ref, this.context);

  Future<void> logout({bool force = true, String? message}) async {
    EasyThrottle.throttle(
      'LogoutController.logout',
      const Duration(seconds: 3),
      () async {
        if (!force) {
          final result = await context.showOkCancelAlertDialog(
              title: 'Logout', content: 'Are you sure you want to logout?');
          if (result != true) return;
        }

        try {
          final isEndVoiceCall = await _endVoiceCall(force: force);
          if (!isEndVoiceCall) return;

          final isEndInstantVoiceCall =
              await _endInstantVoiceCall(force: force);
          if (!isEndInstantVoiceCall) return;

          final isEndRoom = await _cleanUpRoom(force: force);
          if (!isEndRoom) return;

          getIt<IMiniPlayerService>().removeMiniPlayer();

          if (context.mounted) {
            context.pushAndRemoveUntil(
              const WidgetPageConfig(
                page: AuthenticationMainScreen(),
              ),
            );
          }

          await ref.read(accountProvider.notifier).logout();
          await _cleanUpImProvider();
          await _cleanUpGiftProvider();
          // final userId = ref.read(authStateNotifierProvider).userInfo?.profile?.id;
          // if (userId != null) {
          //   await getIt<SignalProtocolService>().clearUserSession(userId);
          // }

          await getIt<NetworkService>().cancelAll();

          // Only invalidate homeProvider - dependent providers will be automatically disposed
          _cleanUpHomeProvider();

          if (context.mounted) {
            if (message != null) {
              LoadingUtils.showToast(message);
            } else {
              LoadingUtils.showToast(context.l10n.loggedOut);
            }
          }
        } catch (e) {
          LogUtils.e('logout error: $e', tag: 'MyHomeScreen');
          LoadingUtils.showError(e.toString());
        }
      },
    );
  }

  Future<void> _cleanUpImProvider() async {
    // ref.invalidate(chatProvider);
    // ref.invalidate(conversationProvider);
    // ref.invalidate(chatSocketProvider);
  }

  Future<void> _cleanUpGiftProvider() async {
    try {
      if (ref.exists(giftStateNotifierProvider)) {
        ref.invalidate(giftStateNotifierProvider);
      }
    } catch (e) {
      LogUtils.d('clean up gift provider error: $e', tag: 'AppSettingsScreen');
    }
  }

  Future<bool> _cleanUpRoom({bool force = true}) async {
    try {
      final roomInfo =
          ref.read(audioRoomProvider.select((state) => state.currentRoom));
      final notifier = ref.read(audioRoomProvider.notifier);

      if (roomInfo != null) {
        if (!force) {
          final result = await context.showOkCancelAlertDialog(
            title: 'End Room',
            content: 'Are you sure you want to end the room?',
          );
          if (result != true) return false;
        }
        if (ref.read(audioRoomProvider).isCreator) {
          await notifier.endRoom();
        } else {
          await notifier.leaveRoom();
        }
      }
    } catch (e) {
      LogUtils.e('clean up room error: $e', tag: 'AppSettingsScreen');
    }

    return true;
  }

  Future<bool> _endVoiceCall({bool force = true}) async {
    try {
      if (ref.exists(friendVoiceCallProvider)) {
        if (ref.read(friendVoiceCallProvider).isCallActive) {
          if (!force) {
            final result = await context.showOkCancelAlertDialog(
              title: 'End Call',
              content: 'Are you sure you want to end the call?',
            );
            if (result != true) return false;
          }
          await ref.read(friendVoiceCallProvider.notifier).endCall();
        }

        // No need to manually invalidate - will be auto-disposed when homeProvider is invalidated
      }
    } catch (e) {
      LogUtils.d('clean up voice call error: $e', tag: 'AppSettingsScreen');
    }

    return true;
  }

  Future<bool> _endInstantVoiceCall({bool force = true}) async {
    try {
      if (ref.exists(instantCallProvider)) {
        if (ref.read(instantCallProvider).isCallActive) {
          if (!force) {
            final result = await context.showOkCancelAlertDialog(
              title: 'End Call',
              content: 'Are you sure you want to end the call?',
            );
            if (result != true) return false;
          }
          await ref.read(instantCallProvider.notifier).endCall();
        }

        // No need to manually invalidate - will be auto-disposed when homeProvider is invalidated
      }
    } catch (e) {
      LogUtils.d('clean up instant voice call error: $e',
          tag: 'AppSettingsScreen');
    }

    return true;
  }

  Future<void> _cleanUpHomeProvider() async {
    try {
      if (ref.exists(homeProvider)) {
        ref.invalidate(homeProvider);
      }
    } catch (e) {
      LogUtils.d('clean up home provider error: $e', tag: 'AppSettingsScreen');
    }
  }
}

class AppSettingsScreen extends ConsumerStatefulWidget {
  const AppSettingsScreen({super.key});

  @override
  ConsumerState<AppSettingsScreen> createState() => _AppSettingsScreenState();
}

class _AppSettingsScreenState extends ConsumerState<AppSettingsScreen> {
  List<SettingsSection> get _sections => [
        SettingsSection(
          title: 'Account',
          options: [
            AppSettingsOptions(
              title: 'Blacklist',
              icon: CupertinoIcons.person_crop_circle_badge_xmark,
              iconColor: _getIconColor(context),
              onPressed: () => context.push(const WidgetPageConfig(
                page: BlacklistScreen(),
              )),
            ),
            AppSettingsOptions(
              title: 'Change Password',
              icon: CupertinoIcons.lock,
              iconColor: _getIconColor(context),
              onPressed: () => context.push(const WidgetPageConfig(
                page: ChangePasswordScreen(),
              )),
              showDivider: false,
            ),
          ],
        ),
        if (_hasUserInfo())
          SettingsSection(
            title: 'Account Information',
            options: _buildUserInfoOptions(),
          ),
        SettingsSection(
          options: [
            AppSettingsOptions(
              title: 'Logout',
              icon: CupertinoIcons.arrow_right_square,
              iconColor: Colors.blue,
              onPressed: () {
                LogoutController(providerContainer, context).logout(
                  force: false,
                );
              },
              showDivider: false,
            ),
          ],
        ),
        SettingsSection(
          options: [
            AppSettingsOptions(
              title: 'Delete Account',
              icon: CupertinoIcons.delete,
              iconColor: Colors.red,
              onPressed: () {
                context
                    .push(const WidgetPageConfig(page: DeleteAccountScreen()));
              },
              showDivider: false,
            ),
          ],
        ),
      ];

  bool _hasUserInfo() {
    final userInfo = ref.watch(accountProvider).userInfo?.profile;
    return userInfo != null && userInfo.toJson().isNotEmpty;
  }

  Color _getIconColor(BuildContext context) {
    return Theme.of(context).brightness == Brightness.dark
        ? Colors.grey.shade400
        : Colors.grey.shade700;
  }

  List<AppSettingsOptions> _buildUserInfoOptions() {
    final userInfoEntries = ref
            .watch(accountProvider)
            .userInfo
            ?.profile
            ?.toJson()
            .entries
            .where((entry) => entry.value != null)
            .toList() ??
        [];

    final options = <AppSettingsOptions>[];

    for (var i = 0; i < userInfoEntries.length; i++) {
      final item = userInfoEntries[i];
      options.add(
        AppSettingsOptions(
          title: item.key,
          trailing: Text(
            item.value.toString(),
            style: TextStyle(
              color: context.colorScheme.onSurface.withValues(alpha: 0.7),
              fontSize: 14.sp,
            ),
            overflow: TextOverflow.ellipsis,
          ),
          onPressed: () async {
            await Clipboard.setData(ClipboardData(text: item.value.toString()));
            LoadingUtils.showToast('${item.key} copied to clipboard');
          },
          showDivider: i < userInfoEntries.length - 1,
        ),
      );
    }

    return options;
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final isDark = theme.brightness == Brightness.dark;

    return AppScaffold(
      appBar: AppBar(
        title: const Text('Settings'),
        backgroundColor: theme.colorScheme.surface,
        elevation: 0,
      ),
      backgroundColor: isDark
          ? theme.colorScheme.surface.withValues(alpha: 0.9)
          : Color.lerp(theme.colorScheme.surface, Colors.grey.shade100, 0.5),
      contentPadding: EdgeInsets.zero,
      body: SafeArea(
        child: ListView.builder(
          padding: EdgeInsets.symmetric(vertical: 20.h),
          itemCount: _sections.length,
          itemBuilder: (context, sectionIndex) {
            return _buildSection(_sections[sectionIndex], sectionIndex);
          },
        ),
      ),
    );
  }

  Widget _buildSection(SettingsSection section, int sectionIndex) {
    final theme = Theme.of(context);
    final isDark = theme.brightness == Brightness.dark;

    // 为卡片颜色增加微妙的差异
    final cardColor = isDark
        ? Color.lerp(theme.colorScheme.surface, Colors.grey.shade800, 0.3)
        : Color.lerp(theme.colorScheme.surface, Colors.white, 0.8);

    return Padding(
      padding: EdgeInsets.only(bottom: 16.h),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          if (section.title != null)
            Padding(
              padding: EdgeInsets.only(left: 16.w, bottom: 8.h),
              child: Text(
                section.title!,
                style: TextStyle(
                  fontSize: 13.sp,
                  fontWeight: FontWeight.w500,
                  color: context.colorScheme.onSurface.withValues(alpha: 0.7),
                ),
              ),
            ),
          Container(
            margin: EdgeInsets.symmetric(horizontal: 16.w),
            decoration: BoxDecoration(
              color: cardColor,
              borderRadius: BorderRadius.circular(10.r),
              boxShadow: [
                BoxShadow(
                  color: Colors.black.withValues(
                      alpha: Theme.of(context).brightness == Brightness.dark
                          ? 0.3
                          : 0.05),
                  blurRadius: 1,
                  offset: const Offset(0, 1),
                ),
              ],
            ),
            child: ClipRRect(
              borderRadius: BorderRadius.circular(10.r),
              child: Column(
                children: section.options.asMap().entries.map((entry) {
                  final itemIndex = entry.key;
                  final option = entry.value;
                  return _buildOptionItem(
                      option, itemIndex == section.options.length - 1);
                }).toList(),
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildOptionItem(AppSettingsOptions option, bool isLast) {
    return Column(
      children: [
        GestureDetector(
          onTap: option.onPressed,
          behavior: HitTestBehavior.opaque,
          child: Padding(
            padding: EdgeInsets.symmetric(
              horizontal: 16.w,
              vertical: 12.h,
            ),
            child: Row(
              children: [
                if (option.icon != null) ...[
                  Icon(
                    option.icon,
                    size: 22.sp,
                    color: option.iconColor ?? context.colorScheme.onSurface,
                  ),
                  SizedBox(width: 12.w),
                ],
                Expanded(
                  child: Text(
                    option.title,
                    style: TextStyle(
                      fontSize: 16.sp,
                      color: context.colorScheme.onSurface,
                    ),
                  ),
                ),
                option.trailing ??
                    Icon(
                      CupertinoIcons.chevron_right,
                      size: 16.sp,
                      color:
                          context.colorScheme.onSurface.withValues(alpha: 0.5),
                    ),
              ],
            ),
          ),
        ),
        if (!isLast && option.showDivider)
          Padding(
            padding: EdgeInsets.only(left: option.icon != null ? 50.w : 16.w),
            child: Divider(
              height: 1,
              thickness: 0.5,
              color: context.colorScheme.onSurface.withValues(alpha: 0.1),
            ),
          ),
      ],
    );
  }
}
