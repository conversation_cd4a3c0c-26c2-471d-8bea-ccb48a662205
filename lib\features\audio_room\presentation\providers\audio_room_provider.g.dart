// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'audio_room_provider.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$audioRoomHash() => r'a81c2dd7d16edb1578c7023b7b6a3adfd5a1b1f3';

/// See also [AudioRoom].
@ProviderFor(AudioRoom)
final audioRoomProvider = NotifierProvider<AudioRoom, AudioRoomState>.internal(
  AudioRoom.new,
  name: r'audioRoomProvider',
  debugGetCreateSourceHash:
      const bool.fromEnvironment('dart.vm.product') ? null : _$audioRoomHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef _$AudioRoom = Notifier<AudioRoomState>;
// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member
