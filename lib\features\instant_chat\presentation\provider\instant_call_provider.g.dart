// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'instant_call_provider.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$instantCallHash() => r'902a3bfae58c3786407781afe1cacb3f07b56b0f';

/// See also [InstantCall].
@ProviderFor(InstantCall)
final instantCallProvider =
    NotifierProvider<InstantCall, VoiceCallState>.internal(
  InstantCall.new,
  name: r'instantCallProvider',
  debugGetCreateSourceHash:
      const bool.fromEnvironment('dart.vm.product') ? null : _$instantCallHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef _$InstantCall = Notifier<VoiceCallState>;
// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member
