// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'friend_voice_call_provider.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$friendVoiceCallHash() => r'743d4f3017f379f6b44860c1f09f46d1b220d95b';

/// See also [FriendVoiceCall].
@ProviderFor(FriendVoiceCall)
final friendVoiceCallProvider =
    NotifierProvider<FriendVoiceCall, VoiceCallState>.internal(
  FriendVoiceCall.new,
  name: r'friendVoiceCallProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
      ? null
      : _$friendVoiceCallHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef _$FriendVoiceCall = Notifier<VoiceCallState>;
// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member
