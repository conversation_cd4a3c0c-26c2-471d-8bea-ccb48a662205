// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'friend_voice_call_provider.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$friendVoiceCallHash() => r'b08b3624b6aa2732a0592fa367417dcda4487ca6';

/// See also [FriendVoiceCall].
@ProviderFor(FriendVoiceCall)
final friendVoiceCallProvider =
    NotifierProvider<FriendVoiceCall, VoiceCallState>.internal(
  FriendVoiceCall.new,
  name: r'friendVoiceCallProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
      ? null
      : _$friendVoiceCallHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef _$FriendVoiceCall = Notifier<VoiceCallState>;
// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member
