import 'package:riverpod_annotation/riverpod_annotation.dart';

part 'session_provider.g.dart';

/// Session provider that manages user session state
/// This provider is auto-dispose and will be disposed when no longer watched
/// Other providers can depend on this to be automatically cleaned up on logout
@riverpod
class UserSession extends _$UserSession {
  @override
  bool build() {
    // Return true when user is logged in
    return true;
  }

  /// End the user session - this will dispose all dependent providers
  void endSession() {
    // Invalidate this provider, which will cause all dependent providers to be disposed
    ref.invalidateSelf();
  }
}
